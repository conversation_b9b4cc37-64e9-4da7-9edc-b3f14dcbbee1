#!/usr/bin/env python3
"""
Test JSON parsing fixes for the Streamlit Research Agent.
This tests the specific issue with JSON parsing that was causing errors.
"""

import json

def test_json_parsing_robustness():
    """Test robust JSON parsing for malformed responses."""

    # Test cases that might come from LLM responses
    test_cases = [
        # Case 1: Valid JSON
        '{"sections": [{"name": "Introduction", "description": "Overview", "research": false}]}',

        # Case 2: JSO<PERSON> with extra newlines (the reported issue)
        '\n "sections": [{"name": "Introduction", "description": "Overview", "research": false}]',

        # Case 3: <PERSON><PERSON><PERSON> missing opening brace
        '"sections": [{"name": "Introduction", "description": "Overview", "research": false}]}',

        # Case 4: JSO<PERSON> with text before and after
        'Here is the JSON:\n{"sections": [{"name": "Introduction", "description": "Overview", "research": false}]}\nThat\'s the structure.',

        # Case 5: Malformed JSON
        '{"sections": [{"name": "Introduction", "description": "Overview", "research": false]',
    ]

    def robust_json_parse(content: str):
        """Robust JSON parsing function."""
        try:
            # First try direct parsing
            return json.loads(content)
        except json.JSONDecodeError:
            # Try to find JSON in the content
            content = content.strip()

            # Look for JSON object boundaries
            start_idx = content.find('{')
            if start_idx == -1:
                # Try to add missing opening brace
                if content.startswith('"'):
                    content = '{' + content
                    start_idx = 0
                else:
                    raise ValueError("No JSON object found")

            # Find the matching closing brace
            brace_count = 0
            end_idx = -1
            for i in range(start_idx, len(content)):
                if content[i] == '{':
                    brace_count += 1
                elif content[i] == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i
                        break

            if end_idx == -1:
                # Try to add missing closing brace
                content = content + '}'
                end_idx = len(content) - 1

            # Extract and parse JSON
            json_str = content[start_idx:end_idx + 1]
            return json.loads(json_str)

    print("Testing JSON parsing robustness...")

    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest Case {i}: {repr(test_case[:50])}...")

        try:
            result = robust_json_parse(test_case)
            print(f"✅ Successfully parsed: {result}")
        except Exception as e:
            print(f"❌ Failed to parse: {e}")

    return True

def test_specific_error_case():
    """Test the specific error case reported: '\n "sections"'"""

    print("\n" + "="*50)
    print("Testing specific error case: '\\n \"sections\"'")

    # This is the exact error case reported
    error_case = '\n "sections"'

    print(f"Input: {repr(error_case)}")

    # This would be the type of response that might cause this
    possible_responses = [
        '\n "sections": [{"name": "Introduction", "description": "Overview", "research": false}]',
        '\n\n"sections": [{"name": "Introduction", "description": "Overview", "research": false}]',
        '"sections": [{"name": "Introduction", "description": "Overview", "research": false}]',
    ]

    for response in possible_responses:
        print(f"\nTesting response: {repr(response[:50])}...")

        try:
            # Try to fix by adding missing brace
            if response.strip().startswith('"'):
                fixed_response = '{' + response.strip() + '}'
                result = json.loads(fixed_response)
                print(f"✅ Fixed and parsed: {result}")
            else:
                result = json.loads(response)
                print(f"✅ Parsed directly: {result}")
        except Exception as e:
            print(f"❌ Still failed: {e}")

def main():
    """Run all JSON parsing tests."""
    print("🧪 Testing JSON Parsing Fixes")
    print("=" * 50)

    try:
        test_json_parsing_robustness()
        test_specific_error_case()

        print("\n" + "=" * 50)
        print("✅ JSON parsing tests completed!")
        print("\nRecommendations for the Streamlit app:")
        print("1. Implement robust JSON parsing in generate_report_plan")
        print("2. Add fallback handling for malformed JSON responses")
        print("3. Use structured output with proper error handling")

        return True

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)